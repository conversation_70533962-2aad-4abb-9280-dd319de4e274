import { computed } from 'vue'
let locales = computed(() => JSON.parse(sessionStorage.getItem("locales")) || {})

export let stationColumn1 = [
    {
        key: "systemName",
        title: locales.value.dianz<PERSON>ming<PERSON>,
        dataIndex: "systemName",
        tooltip: true
    },
    { key: "countries", title: locales.value.guojia, dataIndex: "countries", tooltip: true },
    {
        key: "provinceName",
        title: locales.value.shengdiqu,
        dataIndex: "provinceName",
        tooltip: true
    },
    { key: "cityName", title: locales.value.chengshi, dataIndex: "cityName", tooltip: true },
    {
        key: "streetName",
        title: locales.value.jiedao,
        dataIndex: "streetName",
        tooltip: true
    },
    { key: "zipCode", title: locales.value.youbian, dataIndex: "zipCode", tooltip: true },
]

export const stationColumn2 = [
    // { key: "systemNo", title: "编号", dataIndex: "systemNo", tooltip: true },
    {
        key: "systemName",
        title: locales.value.dian<PERSON><PERSON><PERSON><PERSON>,
        dataIndex: "systemName",
        tooltip: true,
        width: 150
    },
    { key: "countries", title: locales.value.guojia, 
        dataIndex: "countries", tooltip: true,
        width: 60
     },
    {
        key: "provinceName",
        title: locales.value.shengdiqu,
        dataIndex: "provinceName"
        , tooltip: true,
         width: 90
    },
    { key: "cityName", title: locales.value.chengshi, dataIndex: "cityName", 
        tooltip: true,
     width: 60
     },
    {
        key: "streetName",
        title: "详细地址",
        dataIndex: "streetName",
        tooltip: true
    },
    { key: "status", title: locales.value.status, dataIndex: "status", tooltip: true, type: 'slot', width: 90 },
    { key: "memberName", title: locales.value.yezhuxingming, dataIndex: "memberName", tooltip: true,
         width: 90
     },
    { key: "grade", title: locales.value.dianzhanjibie, dataIndex: "grade", 
        tooltip: true,
        width: 60
     },
    { key: "createTimeCh", title: locales.value.chaungjianshijian, dataIndex: "createTimeCh", 
        tooltip: true},
    { key: "action", title: "操作", dataIndex: "action", tooltip: false, 
        type: 'slot',  width: 90},
]

export const collectorColumn = [
    {
        key: "cloudName", title: locales.value.name, dataIndex: "cloudName",
        tooltip: true, width: 200
    },
    {
        key: "imei",
        title: locales.value.caijiqibiaoshi,
        dataIndex: "imei",
        tooltip: true
    },
    {
        key: "model",
        title: locales.value.xinghao,
        dataIndex: "model",
        tooltip: true
    },
    { key: "powerStationName", title: locales.value.dianzhanmingcheng, dataIndex: "powerStationName", tooltip: true },
    { key: "softVersion", title: locales.value.ruanjianbanbenhao, dataIndex: "softVersion", tooltip: true },
    {
        key: "bootPartition",
        title: locales.value.suozaifenqu,
        dataIndex: "bootPartition",
        tooltip: true
    },
    { key: "createTimeCh", title: locales.value.lurushijan, dataIndex: "createTimeCh", tooltip: true, width: 180 },
    { key: "updateTime", title: locales.value.gengxinshijian, dataIndex: "updateTime", tooltip: true, width: 180 },
]

export const repeaterColumn = [
    {
        key: "relayName", title: locales.value.zhongjiqimingcheng, dataIndex: "relayName",
        tooltip: true
    },
    {
        key: "relayId",
        title: locales.value.zhongjiqibiaoshi,
        dataIndex: "relayId",
        tooltip: true
    },
    { key: "cloudId", title: locales.value.caijiqi, dataIndex: "cloudId", tooltip: true },
    { key: "powerStationName", title: locales.value.dianzhanmingcheng, dataIndex: "powerStationName", tooltip: true },
    { key: "softVersion", title: locales.value.ruanjianbanbenhao, dataIndex: "softVersion", tooltip: true },
    {
        key: "bootPartition",
        title: locales.value.fenqu,
        dataIndex: "bootPartition",
        tooltip: true
    },
    { key: "createTime", title: locales.value.lurushijan, dataIndex: "createTime", tooltip: true, width: 180 },
    { key: "updateTime", title: locales.value.gengxinshijian, dataIndex: "updateTime", tooltip: true, width: 180 },
]

export const inverterColumn = [
    {
        key: "inverterName", title: locales.value.name, dataIndex: "inverterName",
        tooltip: true
    },
    {
        key: "chipId",
        title: locales.value.nibianqibiaoshi,
        dataIndex: "chipId",
        tooltip: true
    },
    { key: "producers", title: locales.value.shengchanshang, dataIndex: "producers", tooltip: true },
    { key: "model", title: locales.value.xinghao, dataIndex: "model", tooltip: true },
    { key: "controller", title: locales.value.kongzhiqi, dataIndex: "controller", tooltip: true },
    {
        key: "power",
        title: locales.value.edinggonglv,
        dataIndex: "power",
        tooltip: true
    },
    { key: "powerStationName", title: locales.value.dianzhanmingcheng, dataIndex: "powerStationName", tooltip: true, width: 180 },
    { key: "createTimeCh", title: locales.value.lurushijan, dataIndex: "createTimeCh", tooltip: true, width: 180 },
]

export const groupColumn = [
    {
        key: "groupName", title: locales.value.zuchuanname, dataIndex: "groupName",
        tooltip: true
    },
    {
        key: "chipId",
        title: locales.value.zuchuanbiaoshi,
        dataIndex: "chipId",
        tooltip: true
    },
    { key: "powerStationName", title: locales.value.dianzhanmingcheng, dataIndex: "powerStationName", tooltip: true },
    { key: "inverterName", title: locales.value.nibianqi, dataIndex: "inverterName", tooltip: true },
    { key: "cloudName", title: locales.value.caijiqi, dataIndex: "cloudName", tooltip: true },
    {
        key: "power",
        title: locales.value.edinggonglv,
        dataIndex: "power",
        tooltip: true
    },
    { key: "groupNum", title: locales.value.zujianshuliang, dataIndex: "groupNum", tooltip: true },
    { key: "groupType", title: locales.value.leixing, dataIndex: "groupType", tooltip: true },
    { key: "createUserName", title: locales.value.lururen, dataIndex: "createUserName", tooltip: true },
    { key: "createTimeCh", title: locales.value.lurushijan, dataIndex: "createTimeCh", tooltip: true, width: 180 },
]

export const componentColumn = [
    {
        key: "chipId", title: locales.value.zujianzuobiao, dataIndex: "chipId",
        tooltip: true
    },
    {
        key: "belongsGroupName",
        title: locales.value.suoshuzuchuan,
        dataIndex: "belongsGroupName",
        tooltip: true
    },
    { key: "powerStationName", title: locales.value.dianzhanmingcheng, dataIndex: "powerStationName", tooltip: true },
    { key: "imei", title: locales.value.caijiqibiaoshi, dataIndex: "imei", tooltip: true },
    { key: "softVersion", title: locales.value.ruanjianbanbenhao, dataIndex: "softVersion", tooltip: true },
    {
        key: "bootPartition",
        title: locales.value.fenqu,
        dataIndex: "bootPartition",
        tooltip: true
    },
    { key: "createTimeCh", title: locales.value.lurushijan, dataIndex: "createTimeCh", tooltip: true, width: 180 },
    { key: "updateTime", title: locales.value.gengxinshijian, dataIndex: "updateTime", tooltip: true, width: 180 },
]

export const warningColumn = [
    {
        key: "warningType", title: locales.value.jinggaoleixing, dataIndex: "warningType",
        tooltip: true
    },
    { key: "equipmentType", title: locales.value.shebeileixing, dataIndex: "equipmentType", tooltip: true },
    { key: "warningContent", title: locales.value.jinggaoneirong, dataIndex: "warningContent", tooltip: true, width: 380 },
    {
        key: "systemName",
        title: locales.value.dianzhanmingcheng,
        dataIndex: "systemName",
        tooltip: true
    },
    { key: "createTime", title: locales.value.fashengshijian, dataIndex: "createTime", tooltip: true, width: 180 },
    {
        key: "warningStatus",
        title: locales.value.status,
        dataIndex: "warningStatus",
        tooltip: true,
        type: 'slot',
        width: 100
    },
    {
        key: "action",
        title: "操作",
        dataIndex: "action",
        tooltip: false,
        type: 'slot',
        width: 180
    },
]

export const versionColumn = [
    {
        key: "versionId", title: locales.value.banbenbiaoshi, dataIndex: "versionId",
        tooltip: true,
        width: 180
    },
    {
        key: "fileName",
        title: locales.value.banbenwenjian,
        dataIndex: "fileName",
        tooltip: true,
        width: 400
    },
    { key: "boardType", title: locales.value.banbenleixing, dataIndex: "boardType", tooltip: true },
    { key: "firmware", title: locales.value.gujianbanben, dataIndex: "firmware", tooltip: true },
    { key: "mcuSupport", title: "mcu", dataIndex: "mcuSupport", tooltip: true },
    {
        key: "bomSupport",
        title: "bomId",
        dataIndex: "bomSupport",
        tooltip: true
    },
    { key: "createTime", title: locales.value.banbenshangchuanshijian, dataIndex: "createTime", tooltip: true, width: 180 },
]

export const upgradeTaskColumn = [
    {
        key: "taskId", title: locales.value.renwubiaoshi, dataIndex: "taskId",
        tooltip: true
    },
    {
        key: "fileName",
        title: locales.value.banbenwenjian,
        dataIndex: "fileName",
        tooltip: true,
        width: 280
    },
    { key: "type", title: locales.value.banbenleixing, dataIndex: "type", tooltip: true },
    { key: "imei", title: locales.value.caijiqibiaoshi, dataIndex: "imei", tooltip: true },
    { key: "taskStatus", title: locales.value.renwuzhuangtai, dataIndex: "taskStatus", tooltip: true, width: 180 },
    { key: "updateBeginTime", title: locales.value.renwukaishishijian, dataIndex: "updateBeginTime", tooltip: true, width: 180 },
    { key: "updateTime", title: locales.value.gengxinshijian, dataIndex: "updateTime", tooltip: true, width: 180 },
]

export const queryTaskColumn = [
    {
        key: "taskId", title: locales.value.renwubiaoshi, dataIndex: "taskId",
        tooltip: true
    },
    {
        key: "queryId",
        title: locales.value.chaxunbiaoshi,
        dataIndex: "queryId",
        tooltip: true
    },
    { key: "type", title: locales.value.chaxunbiaoshileixing, dataIndex: "type", tooltip: true },
    { key: "queryType", title: locales.value.chaxunleixing, dataIndex: "queryType", tooltip: true },
    { key: "imei", title: locales.value.caijiqiimei, dataIndex: "imei", tooltip: true },
    { key: "status", title: locales.value.status, dataIndex: "status", tooltip: true },
    { key: "beginTime", title: locales.value.renwukaishishijian, dataIndex: "beginTime", tooltip: true, width: 180 },
    { key: "updateTime", title: locales.value.gengxinshijian, dataIndex: "updateTime", tooltip: true, width: 180 },
]

export const selectCloudColumn = [
    { key: "cloudName", title: locales.value.name, dataIndex: "cloudName", tooltip: true },
    { key: "serialNo", title: locales.value.xuliehao, dataIndex: "serialNo", tooltip: true },
    { key: "powerStationName", title: locales.value.suoshudianzhan, dataIndex: "powerStationName", tooltip: true },
    { key: "createTimeCh", title: locales.value.chaungjianshijian, dataIndex: "createTimeCh", tooltip: true },
]

export const selectInverterColumn = [
    { key: "inverterName", title: locales.value.name, dataIndex: "inverterName", tooltip: true },
    { key: "producers", title: locales.value.shengchanshang, dataIndex: "producers", tooltip: true },
    { key: "model", title: locales.value.xinghao, dataIndex: "model", tooltip: true },
    { key: "powerStationName", title: locales.value.suoshudianzhan, dataIndex: "powerStationName", tooltip: true },
    { key: "controller", title: locales.value.kongzhiqi, dataIndex: "controller", tooltip: true },
    { key: "createUserName", title: locales.value.lururen, dataIndex: "createUserName", tooltip: true },
    { key: "createTimeCh", title: locales.value.lurushijan, dataIndex: "createTimeCh", tooltip: true },
]

export const selectComponentColumn = [
    { key: "producers", title: locales.value.shengchanshang, dataIndex: "producers", tooltip: true },
    { key: "componentNo", title: locales.value.chuchangqianxuliehao, dataIndex: "componentNo", tooltip: true },
    { key: "model", title: locales.value.xinghao, dataIndex: "model", tooltip: true },
    { key: "chipId", title: locales.value.zujianzuobiao, dataIndex: "chipId", tooltip: true },
    {
        key: "belongsGroupName",
        title: locales.value.suoshuzuchuan,
        dataIndex: "belongsGroupName",
        tooltip: true
    },
    { key: "powerStationName", title: locales.value.suoshudianzhan, dataIndex: "powerStationName", tooltip: true },
    {
        key: "status", title: locales.value.status, dataIndex: "status", tooltip: true, options: [
            { label: locales.value.dakai, value: 1 },
            { label: locales.value.close, value: 2 }
        ]
    },
    { key: "createUserName", title: locales.value.lururen, dataIndex: "createUserName", tooltip: true },
    { key: "createTimeCh", title: locales.value.lurushijan, dataIndex: "createTimeCh", tooltip: true },
]

export const selectOwnerColumn = [
    { key: "phone", title: locales.value.yonghuzhanghao, dataIndex: "phone", tooltip: true },
    { key: "name", title: locales.value.ming, dataIndex: "name", tooltip: true },
    { key: "xing", title: locales.value.xing, dataIndex: "xing", tooltip: true },
    { key: "membertype", title: locales.value.yonghuleixing, dataIndex: "membertype", tooltip: true },
    { key: "memberstatus", title: locales.value.yonghuzhuangtai, dataIndex: "memberstatus", tooltip: true },
    { key: "createTimeCh", title: locales.value.zhuceshijian, dataIndex: "createTimeCh", tooltip: true },
]

const groupTypeOpt = [
    { label: "独立", value: 1 },
    { label: "并联", value: 2 },
];
export const selectGroupColumn = [
    {
        key: "groupName", title: locales.value.zuchuanname, dataIndex: "groupName",
        tooltip: true
    },
    { key: "inverterName", title: locales.value.nibianqi, dataIndex: "inverterName", tooltip: true },
    { key: "groupType", title: locales.value.leixing, dataIndex: "groupType", tooltip: true, options: groupTypeOpt },
    { key: "createUserName", title: locales.value.lururen, dataIndex: "createUserName", tooltip: true },
    { key: "createTimeCh", title: locales.value.lurushijan, dataIndex: "createTimeCh", tooltip: true, width: 180 },
]

export const selectRelayColumn = [
    {
        key: "relayName", title: locales.value.zhongjiqimingcheng, dataIndex: "relayName",
        tooltip: true
    },
    {
        key: "relayId",
        title: locales.value.zhongjiqibiaoshi,
        dataIndex: "relayId",
        tooltip: true
    },
    { key: "hardVersion", title: locales.value.yingjianbanbenhao, dataIndex: "hardVersion", tooltip: true },
    { key: "softVersion", title: locales.value.ruanjianbanbenhao, dataIndex: "softVersion", tooltip: true },
]

export const viewComponentColumn = [
    {
        key: "chipId", title: locales.value.zujianzuobiao, dataIndex: "chipId",
        tooltip: true
    },
    {
        key: "belongsGroupName",
        title: locales.value.zuchuanname,
        dataIndex: "belongsGroupName",
        tooltip: true
    },
    { key: "imei", title: locales.value.caijiqibiaoshi, dataIndex: "imei", tooltip: true },
    { key: "powerStationName", title: locales.value.dianzhanmingcheng, dataIndex: "powerStationName", tooltip: true },
]

export const viewSelectComponentColumn = [
    { key: "producers", title: locales.value.shengchanshang, dataIndex: "producers", tooltip: true },
    { key: "componentNo", title: locales.value.chuchangqianxuliehao, dataIndex: "componentNo", tooltip: true },
    { key: "model", title: locales.value.xinghao, dataIndex: "model", tooltip: true },
    { key: "chipId", title: locales.value.zujianzuobiao, dataIndex: "chipId", tooltip: true },
    {
        key: "belongsGroupName",
        title: locales.value.suoshuzuchuan,
        dataIndex: "belongsGroupName",
        tooltip: true
    },
    { key: "powerStationName", title: locales.value.dianzhanmingcheng, dataIndex: "powerStationName", tooltip: true },
    {
        key: "status", title: locales.value.status, dataIndex: "status", tooltip: true, options: [
            { label: locales.value.dakai, value: 1 },
            { label: locales.value.close, value: 2 }
        ]
    },
    { key: "createUserName", title: locales.value.lururen, dataIndex: "createUserName", tooltip: true },
    { key: "createTimeCh", title: locales.value.lurushijan, dataIndex: "createTimeCh", tooltip: true },
]

export const queryResultColumn = [
    { key: "taskId", title: locales.value.renwubiaoshi, dataIndex: "taskId", tooltip: true },
    { key: "componentId", title: locales.value.zujianbiaoshi, dataIndex: "componentId", tooltip: true },
    { key: "softVersion", title: locales.value.gujianbanben, dataIndex: "softVersion", tooltip: true },
    { key: "bootPartition", title: locales.value.fenqu, dataIndex: "bootPartition", tooltip: true },
    { key: "updateTime", title: locales.value.gengxinshijian, dataIndex: "updateTime", tooltip: true },
]

const queryStatusOpts = [
    { label: '未查询', value: 0 },
    { label: '已查询', value: 1 },
    { label: '查询采集器没有响应', value: 2 },
];
const updateStatusOpts = [
    { label: '未开始', value: 0 },
    { label: '发送版本成功', value: 1 },
    { label: '发送版本失败', value: 2 },
    { label: '发送版本失败 采集器没有响应', value: 21 },
    { label: '加载中继成功', value: 3 },
    { label: '加载中继失败', value: 4 },
    { label: '加载中继失败 采集器没有响应', value: 41 },
    { label: '升级成功', value: 5 },
    { label: '发送升级信息成功', value: 7 },
    { label: '发送升级信息失败', value: 8 },
    { label: '发送升级信息失败 采集器没有响应', value: 81 },
];
export const updateResultColumn = [
    { key: "taskId", title: locales.value.renwubiaoshi, dataIndex: "taskId", tooltip: true },
    { key: "componentId", title: locales.value.zujianbiaoshi, dataIndex: "componentId", tooltip: true },
    { key: "updateVersion", title: locales.value.xuyaoshengjibanben, dataIndex: "updateVersion", tooltip: true },
    { key: "beforeVersion", title: locales.value.yuanlaibanben, dataIndex: "beforeVersion", tooltip: true },
    { key: "queryVersion", title: locales.value.chaxunorfanhuibanben, dataIndex: "queryVersion", tooltip: true },
    { key: "updateStatus", title: locales.value.shengjizhuangtai, dataIndex: "updateStatus", tooltip: true, options: updateStatusOpts },
    { key: "queryStatus", title: locales.value.chaxunzhuangtai, dataIndex: "queryStatus", tooltip: true, options: queryStatusOpts },
    { key: "updateTime", title: locales.value.gengxinshijian, dataIndex: "updateTime", tooltip: true },
]

const boardTypeOpts = [
    { label: '中继器', value: 'repeater' },
    { label: '采集器', value: 'collector' },
    { label: '优化器', value: 'optimizer' },
];
export const updateVersionColumn = [
    { key: "versionId", title: locales.value.banbenbiaoshi, dataIndex: "versionId", tooltip: true },
    { key: "boardType", title: locales.value.banbenleixing, dataIndex: "boardType", tooltip: true, options: boardTypeOpts },
    { key: "firmware", title: locales.value.gujianbanben, dataIndex: "firmware", tooltip: true },
    { key: "hardware", title: locales.value.yingjianbanbenhao, dataIndex: "hardware", tooltip: true },
    { key: "mcuSupport", title: "mcu", dataIndex: "mcuSupport", tooltip: true },
    { key: "bomSupport", title: "bomId", dataIndex: "bomSupport", tooltip: true },
    { key: "fileName", title: locales.value.banbenwenjian, dataIndex: "fileName", tooltip: true },
    { key: "createTime", title: locales.value.banbenshangchuanshijian, dataIndex: "createTime", tooltip: true },
];
export const updateImeiColumn = [
    { key: "cloudName", title: locales.value.caijiqimingcheng, dataIndex: "cloudName", tooltip: true },
    { key: "imei", title: locales.value.caijiqibiaoshi, dataIndex: "imei", tooltip: true },
    { key: "powerStationName", title: locales.value.suoshudianzhan, dataIndex: "powerStationName", tooltip: true },
    { key: "createTimeCh", title: locales.value.chaungjianshijian, dataIndex: "createTimeCh", tooltip: true },
];
export const updateComponentColumn = [
    { key: "chipId", title: locales.value.zujianbiaoshi, dataIndex: "chipId", tooltip: true },
    { key: "softVersion", title: locales.value.ruanjianbanbenhao, dataIndex: "softVersion", tooltip: true },
    { key: "bootPartition", title: locales.value.suozaifenqu, dataIndex: "bootPartition", tooltip: true },
    { key: "groupName", title: locales.value.zuchuanname, dataIndex: "groupName", tooltip: true },
    { key: "hardVersion", title: locales.value.yingjianbanbenhao, dataIndex: "hardVersion", tooltip: true },
    { key: "mcu", title: "mcu", dataIndex: "mcu", tooltip: true },
    { key: "bomId", title: "bomId", dataIndex: "bomId", tooltip: true },
];

