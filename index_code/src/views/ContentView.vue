<template>
  <div class="content-view wh100">
    <SystemView v-if="path == '/system-view'"></SystemView>
    <StationList v-else-if="path == '/station-list'"></StationList>
    <Map v-else-if="path == '/station-map'"></Map>
    <Equipment v-else-if="path == '/equipment-management'"></Equipment>
    <Collector v-else-if="path == '/collector'"></Collector>
    <Repeater v-else-if="path == '/repeater'"></Repeater>
    <Inverter v-else-if="path == '/inverter'"></Inverter>
    <GroupList v-else-if="path == '/group-list'"></GroupList>
    <ComponentList v-else-if="path == '/component-list'"></ComponentList>
    <Warning v-else-if="path == '/warning'"></Warning>
    <Version v-else-if="path == '/version'"></Version>
    <UpgradeTask v-else-if="path == '/task-upgrade'"></UpgradeTask>
    <QueryTask v-else-if="path == '/task-query'"></QueryTask>
    <div class="pl16" v-else>{{ path }}-content</div>
  </div>
</template>

<script setup>
import { defineAsyncComponent } from "vue";
const SystemView = defineAsyncComponent(() => import("./home/<USER>"));
const Map = defineAsyncComponent(() => import("./components/Map/index.vue"));
const StationList = defineAsyncComponent(() =>
  import("./stationList/index.vue")
);
const Equipment = defineAsyncComponent(() =>
  import("./equipment/equipment.vue")
);
const Collector = defineAsyncComponent(() => import("./collector/index.vue"));
const Repeater = defineAsyncComponent(() => import("./repeater/index.vue"));
const Inverter = defineAsyncComponent(() => import("./inverter/index.vue"));
const GroupList = defineAsyncComponent(() => import("./groupList/index.vue"));
const ComponentList = defineAsyncComponent(() =>
  import("./componentList/index.vue")
);
const Warning = defineAsyncComponent(() => import("./warning/index.vue"));
const Version = defineAsyncComponent(() => import("./version/index.vue"));
const UpgradeTask = defineAsyncComponent(() =>
  import("./upgradeTask/index.vue")
);
const QueryTask = defineAsyncComponent(() => import("./queryTask/index.vue"));

let props = defineProps({
  path: {
    type: String,
    default: "/system-view",
  },
});
</script>