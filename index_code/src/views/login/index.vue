<template>
  <div class="login-view wh100">
    <div class="login-modal flex-column-center-center">
      <div class="title">{{ locale?.login }}</div>
      <a-form
        ref="formRef"
        :model="formState"
        :label-col="{ span: 0 }"
        :wrapper-col="{ span: 24 }"
      >
        <a-form-item
          name="username"
          :rules="[
            {
              required: true,
              message: locale?.usernameCheck,
              trigger: 'focus',
            },
          ]"
        >
          <a-input
            v-model:value="formState.username"
            :placeholder="locale?.usernamePlaceholder"
            @keyup.enter="handleLogin"
          />
        </a-form-item>

        <a-form-item
          name="password"
          :rules="[
            { required: true, message: locale?.pswCheck, trigger: 'focus' },
          ]"
        >
          <a-input-password
            v-model:value="formState.password"
            :placeholder="locale?.pswPlaceholder"
            @keyup.enter="handleLogin"
          />
        </a-form-item>
        <a-form-item name="language">
          <a-select v-model:value="formState.language" :options="languageOpt" />
        </a-form-item>
        <a-form-item :wrapper-col="{ offset: 0, span: 24 }">
          <a-button class="mt16" @click="handleLogin">{{
            locale?.login
          }}</a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { login } from "@/api/list.js";
import { useRouter } from "vue-router";
import { message } from "ant-design-vue";

import locales from "@/locales.json";

const router = useRouter();

const formState = ref({
  username: "",
  password: "",
  language: "zh",
  remember: true,
});
let formRef = ref(null);

const languageOpt = [
  { label: "中文", value: "zh" },
  { label: "English", value: "en" },
  { label: "عربي ،", value: "ar" }, // 阿拉伯语
  { label: "日本語", value: "ja" },
  { label: "Français", value: "fr" }, // 法语
  { label: "Español", value: "es" }, // 西班牙语
];

let locale = computed(() => locales[formState.value.language || "zh"]);

let emit = defineEmits(["loginsuc"]);

function handleLogin() {
  formRef.value
    .validateFields()
    .then(async () => {
      // 校验成功-登录
      let res = await login({
        userId: formState.value.username,
        password: formState.value.password,
      });
      console.log(res, "====1111===");
       debugger;
      if (res?.data?.code === 0) {
        sessionStorage.setItem("userName", formState.value.username);
        sessionStorage.setItem(
          "locales",
          JSON.stringify(locales[formState.value.language || "zh"])
        );
        emit("loginsuc", locales[formState.value.language || "zh"]);
        await router.push("/");
      } else {
        message.warning(locale.value.loginCheck);
        sessionStorage.removeItem("userName");
        sessionStorage.removeItem("locales");
      }
    })
    .catch((errors) => {
      // 校验失败，可以根据需要处理错误信息。
      console.log(errors, "=======");
      return;
    });
}
</script>

<style lang="less" scoped>
.login-view {
  position: relative;
  background-size: cover;
  animation: backgroundFade 6s infinite;

  @keyframes backgroundFade {
    0%,
    100% {
      background-image: url("../../assets/images/login-bg-1.png");
    }
    50% {
      background-image: url("../../assets/images/login-bg-2.png");
    }
  }

  .login-modal {
    position: absolute;
    left: 50%;
    top: 150px;
    transform: translateX(-50%);
    .title {
      margin-bottom: 25px;
      color: #fff;
      font-size: 30px;
      font-weight: 700;
      text-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
    }

    :deep(.ant-form) {
      width: 302px;
      .ant-form-item-control-input-content {
        > .ant-input,
        .ant-input-password,
        .ant-select-selector {
          width: 100%;
          height: 42px;
          font-size: 14px;
          color: #fff;
          background: rgba(45, 45, 45, 0.15);
          border-radius: 6px;
          border: 1px solid rgba(255, 255, 255, 0.15);
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
          overflow: hidden;

          &::placeholder {
            font-size: 14px;
            color: #fff;
          }
          &:focus,
          &.ant-input-affix-wrapper-focused {
            -moz-box-shadow: 0 2px 3px 0 rgba(0, 0, 0, 0.1) inset;
            -webkit-box-shadow: 0 2px 3px 0 rgba(0, 0, 0, 0.1) inset;
            box-shadow: 0 2px 3px 0 rgba(0, 0, 0, 0.1) inset;
            border: 1px solid rgba(255, 255, 255, 0.15);
          }
          .ant-select-selection-item {
            line-height: 40px !important;
          }
          > .ant-input {
            padding: 4px 11px;
            font-size: 14px;
            color: #fff;
            border: none;
            background: transparent;
            &::placeholder {
              font-size: 14px;
              color: #fff;
            }
          }
          &.ant-input-affix-wrapper {
            padding: 0;
          }
          .ant-input-suffix {
            display: none;
            > .anticon {
              color: #fff;
            }
          }
        }
        > button {
          width: 300px;
          height: 44px;
          font-size: 16px;
          color: #fff;
          background: #ef4300;
          border: none;
          &:active {
            opacity: 0.5;
          }
          &:hover {
            color: #fff;
          }
        }
      }
    }
  }
}
</style>